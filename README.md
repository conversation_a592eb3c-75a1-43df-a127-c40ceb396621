# 🌟 文件共享系统 - SQLite数据库版

## 📋 更新说明

本次更新将数据库从MySQL迁移到了SQLite，提供了更简单、更便携的部署体验。

### ✨ 新功能特性

- **🗄️ SQLite数据库**: 轻量级数据库，无需额外安装MySQL服务
- **🚀 自动初始化**: 系统首次启动时自动创建数据库和表结构
- **📦 便携部署**: 数据库文件随项目一起，便于备份和迁移
- **🔄 跨设备同步**: 用户在不同设备上登录可看到相同的收藏
- **📊 统计分析**: 支持收藏行为统计和分析
- **⚡ 批量操作**: 支持批量检查收藏状态，提升性能
- **🔒 数据安全**: 服务端验证，防止数据篡改
- **📝 收藏备注**: 支持为收藏添加个人备注

## 🚀 快速开始

### 方式一：一键启动（推荐）

1. **双击运行** `一键启动_SQLite.bat`
2. 系统会自动：
   - 检查Python环境
   - 安装必要依赖
   - 自动创建SQLite数据库
   - 创建示例数据
   - 启动服务器

### 方式二：手动启动

1. **安装依赖**
   ```bash
   pip install -r backend/requirements.txt
   ```

2. **初始化数据库（首次运行）**
   ```bash
   cd backend
   python init_database_sqlite.py
   ```

3. **启动服务器**
   ```bash
   python main.py
   ```

## 🌐 访问地址

- **前端界面**: http://localhost:3000
- **API接口**: http://localhost:8080
- **管理员账户**: admin / admin123

## 📚 API文档

### 收藏功能API

#### 1. 获取收藏列表
```http
GET /api/favorites?page=1&page_size=50
```

#### 2. 切换收藏状态
```http
POST /api/favorites/toggle
Content-Type: application/json

{
    "file_id": 123,
    "notes": "收藏备注（可选）"
}
```

#### 3. 批量检查收藏状态
```http
POST /api/favorites/status
Content-Type: application/json

{
    "file_ids": [123, 124, 125]
}
```

#### 4. 获取收藏统计
```http
GET /api/favorites/stats
```

## 🔧 技术架构

### 后端
- **Python 3.7+**: 主要开发语言
- **MySQL**: 数据库存储
- **Flask**: Web框架
- **PyMySQL**: 数据库连接

### 前端
- **原生JavaScript**: 无框架依赖
- **HTML5 + CSS3**: 现代化UI
- **Fetch API**: 异步数据请求

### 数据库表结构

#### user_favorites (用户收藏表)
```sql
CREATE TABLE user_favorites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    file_id INT NOT NULL,
    favorited_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_file (user_id, file_id)
);
```

## 🔄 迁移说明

### 从localStorage迁移

如果您之前使用了localStorage版本的收藏功能：

1. **导出现有数据**
   - 打开 `frontend/export-favorites.html`
   - 导出现有收藏数据

2. **自动迁移**
   - 运行 `backend/migrate_favorites.py`
   - 或使用一键启动脚本自动处理

3. **清理本地数据**
   - 使用导出工具清理localStorage数据

### 向后兼容

- 前端代码保留了localStorage的回退机制
- 如果API调用失败，会自动使用本地存储
- 提供了完整的数据迁移工具

## 🛠️ 开发说明

### 项目结构
```
├── backend/                    # 后端代码
│   ├── models/                # 数据模型
│   │   └── favorite.py        # 收藏模型
│   ├── services/              # 业务服务
│   │   ├── favorite_service.py          # 完整收藏服务
│   │   └── favorite_service_simple.py  # 简化收藏服务
│   ├── api/                   # API接口
│   ├── auto_migrate.py        # 自动迁移脚本
│   ├── migrate_favorites.py   # 收藏数据迁移
│   └── main.py               # 主程序
├── frontend/                  # 前端代码
│   ├── js/
│   │   ├── api.js            # API封装
│   │   └── file-manager.js   # 文件管理器
│   └── export-favorites.html # 数据导出工具
├── 一键启动.bat              # 启动脚本
└── README.md                 # 说明文档
```

### 添加新功能

1. **后端API**
   - 在 `backend/api/server.py` 中添加路由
   - 在 `backend/services/favorite_service_simple.py` 中添加业务逻辑

2. **前端调用**
   - 在 `frontend/js/api.js` 中添加API方法
   - 在 `frontend/js/file-manager.js` 中调用API

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认用户名密码：root/123456
   - 检查端口3306是否可用

2. **Python依赖缺失**
   ```bash
   pip install pymysql
   ```

3. **端口占用**
   - 前端端口3000被占用：修改 `backend/main.py` 中的端口配置
   - API端口8080被占用：修改 `backend/api/server.py` 中的端口配置

4. **收藏功能不工作**
   - 检查浏览器控制台错误
   - 确认API服务器正常运行
   - 检查用户登录状态

### 日志查看

- **后端日志**: 控制台输出
- **前端日志**: 浏览器开发者工具 Console
- **数据库日志**: MySQL错误日志

## 📞 技术支持

如果遇到问题，请：

1. 查看控制台错误信息
2. 检查 `backend/logs/` 目录下的日志文件
3. 确认数据库连接和表结构
4. 验证API接口是否正常响应

## 🎯 未来计划

- [ ] 收藏夹分组功能
- [ ] 收藏导入/导出
- [ ] 收藏分享功能
- [ ] 智能推荐收藏
- [ ] 收藏标签系统

---

**🎉 享受全新的数据库收藏体验！**
