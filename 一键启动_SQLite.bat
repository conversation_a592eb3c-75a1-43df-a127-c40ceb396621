@echo off
chcp 65001 >nul
title 企业级文件共享系统 - SQLite版本

echo.
echo ========================================
echo   企业级文件共享系统 - SQLite版本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo.
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

:: 检查是否在正确的目录
if not exist "backend\main.py" (
    echo ❌ 错误: 请在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 安装依赖包
echo.
echo 📦 检查并安装依赖包...
pip install -r backend\requirements.txt --quiet

if errorlevel 1 (
    echo ⚠️  警告: 部分依赖包安装失败，但系统可能仍能正常运行
)

:: 检查数据库是否存在
if not exist "backend\data\file_share_system.db" (
    echo.
    echo 🔧 首次运行，正在初始化SQLite数据库...
    cd backend
    python init_database_sqlite.py
    cd ..
    
    if errorlevel 1 (
        echo ❌ 数据库初始化失败
        pause
        exit /b 1
    )
    
    echo ✅ 数据库初始化成功
) else (
    echo ✅ 数据库已存在
)

:: 启动服务器
echo.
echo 🚀 启动文件共享系统...
echo.
echo 默认管理员账户:
echo   用户名: admin
echo   密码: admin123
echo.
echo 系统将自动打开浏览器，如果没有自动打开，请手动访问:
echo   前端地址: http://localhost:8082
echo   API地址: http://localhost:8086
echo.
echo 按 Ctrl+C 停止服务器
echo.

cd backend
python main.py

:: 如果程序异常退出，显示错误信息
if errorlevel 1 (
    echo.
    echo ❌ 服务器启动失败，请检查错误信息
    echo.
    echo 常见解决方案:
    echo 1. 检查端口8082和8086是否被占用
    echo 2. 检查Python依赖包是否完整安装
    echo 3. 检查数据库文件是否正常
    echo.
    pause
)

cd ..
