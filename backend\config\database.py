#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接和管理模块
"""

import sqlite3
import os
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.pool import StaticPool
from contextlib import contextmanager
import logging
from typing import Optional

# 创建基础模型类
Base = declarative_base()

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: dict = None):
        self.config = config or {
            'database_path': 'data/file_share_system.db'
        }
        
        self.engine = None
        self.SessionLocal = None
        self.logger = logging.getLogger(__name__)
    
    def initialize(self):
        """初始化数据库连接"""
        try:
            # 确保数据库目录存在
            db_path = self.config['database_path']
            db_dir = os.path.dirname(db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir, exist_ok=True)

            # 创建数据库连接字符串
            db_url = f"sqlite:///{db_path}"

            # 创建引擎
            self.engine = create_engine(
                db_url,
                poolclass=StaticPool,
                connect_args={
                    'check_same_thread': False,
                    'timeout': 20
                },
                echo=False
            )

            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            # 测试连接
            self.test_connection()

            # 创建数据库表
            self.create_tables()

            self.logger.info("数据库初始化成功")

        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def create_tables(self):
        """创建数据表"""
        try:
            # 导入所有模型
            from models.user import User
            from models.file_share import SharedFolder, SharedFile
            from models.activity_log import ActivityLog
            from models.permission import Permission, UserPermission
            from models.favorite import UserFavorite, FavoriteFolder, FavoriteFolderItem
            
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("数据表创建成功")
            
        except Exception as e:
            self.logger.error(f"创建数据表失败: {e}")
            raise
    
    def test_connection(self):
        """测试数据库连接"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            self.logger.info("数据库连接测试成功")
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {e}")
            raise


    
    @contextmanager
    def get_session(self):
        """获取数据库会话（上下文管理器）"""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            self.logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            session.close()
    
    def get_session_direct(self):
        """直接获取数据库会话"""
        return self.SessionLocal()
    
    def execute_sql(self, sql: str, params: dict = None):
        """执行SQL语句"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text(sql), params or {})
                conn.commit()
                return result
        except Exception as e:
            self.logger.error(f"SQL执行失败: {sql}, 错误: {e}")
            raise
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            self.logger.info("数据库连接已关闭")
    
    def backup_database(self, backup_file: str):
        """备份数据库"""
        try:
            import shutil

            # SQLite数据库备份就是复制文件
            db_path = self.config['database_path']
            if os.path.exists(db_path):
                shutil.copy2(db_path, backup_file)
                self.logger.info(f"数据库备份成功: {backup_file}")
            else:
                raise FileNotFoundError(f"数据库文件不存在: {db_path}")

        except Exception as e:
            self.logger.error(f"数据库备份失败: {e}")
            raise

    def restore_database(self, backup_file: str):
        """恢复数据库"""
        try:
            import shutil

            # SQLite数据库恢复就是复制文件
            db_path = self.config['database_path']
            if os.path.exists(backup_file):
                # 关闭现有连接
                if self.engine:
                    self.engine.dispose()

                # 复制备份文件
                shutil.copy2(backup_file, db_path)

                # 重新初始化连接
                self.initialize()

                self.logger.info(f"数据库恢复成功: {backup_file}")
            else:
                raise FileNotFoundError(f"备份文件不存在: {backup_file}")

        except Exception as e:
            self.logger.error(f"数据库恢复失败: {e}")
            raise
    
    def get_database_stats(self):
        """获取数据库统计信息"""
        try:
            with self.get_session() as session:
                # SQLite获取表信息
                tables_sql = """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                """

                result = session.execute(text(tables_sql))
                table_names = [row[0] for row in result.fetchall()]

                stats = {
                    'tables': [],
                    'total_size': 0,
                    'total_rows': 0
                }

                # 获取数据库文件大小
                db_path = self.config['database_path']
                if os.path.exists(db_path):
                    stats['total_size'] = os.path.getsize(db_path)

                # 获取每个表的行数
                for table_name in table_names:
                    try:
                        count_result = session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        row_count = count_result.scalar()

                        table_info = {
                            'name': table_name,
                            'rows': row_count or 0,
                            'data_size': 0,  # SQLite不提供单表大小信息
                            'index_size': 0
                        }
                        stats['tables'].append(table_info)
                        stats['total_rows'] += table_info['rows']
                    except Exception as e:
                        self.logger.warning(f"获取表 {table_name} 统计信息失败: {e}")

                return stats

        except Exception as e:
            self.logger.error(f"获取数据库统计信息失败: {e}")
            return None
