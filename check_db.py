#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

try:
    # 连接数据库
    conn = pymysql.connect(
        host='localhost',
        user='root', 
        password='123456',
        database='file_share_system'
    )
    cursor = conn.cursor()
    
    # 检查download_records表结构
    print("检查 download_records 表结构:")
    print("-" * 50)
    cursor.execute('DESCRIBE download_records')
    result = cursor.fetchall()
    
    if result:
        print("字段名           | 类型         | 允许空值 | 键   | 默认值")
        print("-" * 60)
        for row in result:
            field, type_info, null, key, default, extra = row
            print(f"{field:<15} | {type_info:<12} | {null:<8} | {key:<4} | {default}")
    else:
        print("download_records 表不存在")
    
    # 检查表是否存在
    print("\n检查所有表:")
    print("-" * 50)
    cursor.execute('SHOW TABLES')
    tables = cursor.fetchall()
    for table in tables:
        print(f"- {table[0]}")
    
    cursor.close()
    conn.close()
    print("\n数据库检查完成!")
    
except Exception as e:
    print(f"数据库连接失败: {e}") 