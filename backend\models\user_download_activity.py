#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户下载活动模型
"""

from sqlalchemy import Column, Integer, String, DateTime, BigInteger, ForeignKey, JSON, Date
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from config.database import Base
from datetime import datetime, date
from typing import Dict, Any, List
import json

class UserDownloadActivity(Base):
    """用户下载活动统计模型 - 记录用户下载行为的汇总数据"""
    
    __tablename__ = 'user_download_activities'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID，NULL表示匿名用户')
    activity_date = Column(Date, nullable=False, comment='活动日期')
    
    # 下载统计
    total_downloads = Column(Integer, default=0, comment='总下载次数')
    single_downloads = Column(Integer, default=0, comment='单文件下载次数')
    batch_downloads = Column(Integer, default=0, comment='批量下载次数')
    folder_downloads = Column(Integer, default=0, comment='文件夹下载次数')
    
    # 大小统计
    total_size = Column(BigInteger, default=0, comment='总下载大小（字节）')
    avg_file_size = Column(BigInteger, default=0, comment='平均文件大小（字节）')
    max_file_size = Column(BigInteger, default=0, comment='最大文件大小（字节）')
    
    # 文件类型统计
    file_types = Column(JSON, nullable=True, comment='文件类型统计 {"pdf": 5, "jpg": 10}')
    
    # 加密下载统计
    encrypted_downloads = Column(Integer, default=0, comment='加密下载次数')
    password_requests = Column(Integer, default=0, comment='密码申请次数')
    
    # 时间统计
    first_download_time = Column(DateTime, nullable=True, comment='首次下载时间')
    last_download_time = Column(DateTime, nullable=True, comment='最后下载时间')
    peak_hour = Column(Integer, nullable=True, comment='下载高峰时段（0-23）')
    
    # 访问信息
    unique_sessions = Column(Integer, default=0, comment='独立会话数')
    unique_ips = Column(Integer, default=0, comment='独立IP数')
    most_used_source = Column(String(50), nullable=True, comment='最常用来源')
    
    # 更新时间
    created_at = Column(DateTime, default=func.now(), comment='创建时间')
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now(), comment='更新时间')
    
    # 关联关系
    user = relationship("User", backref="download_activities")
    
    def __init__(self, user_id: int = None, activity_date: date = None):
        self.user_id = user_id
        self.activity_date = activity_date or date.today()
        self.file_types = {}
    
    def add_download(self, download_type: str, file_size: int, file_extension: str = None, 
                     is_encrypted: bool = False, session_id: str = None, 
                     ip_address: str = None, download_source: str = 'web',
                     download_time: datetime = None):
        """添加下载记录到统计中"""
        
        # 更新基础统计
        self.total_downloads += 1
        self.total_size += file_size
        
        # 更新下载类型统计
        if download_type == 'single':
            self.single_downloads += 1
        elif download_type == 'batch':
            self.batch_downloads += 1
        elif download_type == 'folder':
            self.folder_downloads += 1
        
        # 更新大小统计
        if file_size > self.max_file_size:
            self.max_file_size = file_size
        
        if self.total_downloads > 0:
            self.avg_file_size = self.total_size // self.total_downloads
        
        # 更新文件类型统计
        if file_extension:
            if not self.file_types:
                self.file_types = {}
            ext = file_extension.lower()
            self.file_types[ext] = self.file_types.get(ext, 0) + 1
        
        # 更新加密下载统计
        if is_encrypted:
            self.encrypted_downloads += 1
        
        # 更新时间统计
        now = download_time or datetime.now()
        if not self.first_download_time or now < self.first_download_time:
            self.first_download_time = now
        if not self.last_download_time or now > self.last_download_time:
            self.last_download_time = now
        
        # 更新高峰时段（简单统计，可以后续改进为更精确的计算）
        self.peak_hour = now.hour
        
        # 更新来源统计
        self.most_used_source = download_source
        
        # 更新时间
        self.updated_at = datetime.now()
    
    def add_password_request(self):
        """添加密码申请统计"""
        self.password_requests += 1
        self.updated_at = datetime.now()
    
    def get_file_type_stats(self) -> Dict[str, int]:
        """获取文件类型统计"""
        return self.file_types or {}
    
    def get_top_file_types(self, limit: int = 5) -> List[Dict[str, Any]]:
        """获取最常下载的文件类型"""
        if not self.file_types:
            return []
        
        sorted_types = sorted(self.file_types.items(), key=lambda x: x[1], reverse=True)
        return [{'type': ext, 'count': count} for ext, count in sorted_types[:limit]]
    
    def get_download_efficiency(self) -> float:
        """获取下载效率（加密下载占比）"""
        if self.total_downloads == 0:
            return 0.0
        return self.encrypted_downloads / self.total_downloads
    
    def get_avg_downloads_per_session(self) -> float:
        """获取每个会话的平均下载量"""
        if self.unique_sessions == 0:
            return 0.0
        return self.total_downloads / self.unique_sessions
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'activity_date': self.activity_date.isoformat() if self.activity_date else None,
            'total_downloads': self.total_downloads,
            'single_downloads': self.single_downloads,
            'batch_downloads': self.batch_downloads,
            'folder_downloads': self.folder_downloads,
            'total_size': self.total_size,
            'avg_file_size': self.avg_file_size,
            'max_file_size': self.max_file_size,
            'file_types': self.file_types,
            'encrypted_downloads': self.encrypted_downloads,
            'password_requests': self.password_requests,
            'first_download_time': self.first_download_time.isoformat() if self.first_download_time else None,
            'last_download_time': self.last_download_time.isoformat() if self.last_download_time else None,
            'peak_hour': self.peak_hour,
            'unique_sessions': self.unique_sessions,
            'unique_ips': self.unique_ips,
            'most_used_source': self.most_used_source,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'download_efficiency': self.get_download_efficiency(),
            'avg_downloads_per_session': self.get_avg_downloads_per_session(),
            'top_file_types': self.get_top_file_types()
        }

class UserDownloadSummary(Base):
    """用户下载汇总统计模型 - 记录用户的总体下载统计"""
    
    __tablename__ = 'user_download_summaries'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=True, comment='用户ID，NULL表示匿名用户')
    
    # 总体统计
    total_downloads = Column(Integer, default=0, comment='总下载次数')
    total_size = Column(BigInteger, default=0, comment='总下载大小（字节）')
    total_files = Column(Integer, default=0, comment='总文件数')
    
    # 类型统计
    single_downloads = Column(Integer, default=0, comment='单文件下载次数')
    batch_downloads = Column(Integer, default=0, comment='批量下载次数')
    folder_downloads = Column(Integer, default=0, comment='文件夹下载次数')
    
    # 加密统计
    encrypted_downloads = Column(Integer, default=0, comment='加密下载次数')
    password_requests = Column(Integer, default=0, comment='密码申请次数')
    successful_requests = Column(Integer, default=0, comment='成功申请次数')
    
    # 时间统计
    first_download = Column(DateTime, nullable=True, comment='首次下载时间')
    last_download = Column(DateTime, nullable=True, comment='最后下载时间')
    most_active_day = Column(Date, nullable=True, comment='最活跃日期')
    most_active_hour = Column(Integer, nullable=True, comment='最活跃时段')
    
    # 偏好统计
    favorite_file_types = Column(JSON, nullable=True, comment='偏好文件类型统计')
    preferred_download_source = Column(String(50), nullable=True, comment='偏好下载来源')
    avg_batch_size = Column(Integer, default=0, comment='平均批次大小')
    
    # 活动统计
    active_days = Column(Integer, default=0, comment='活跃天数')
    unique_sessions = Column(Integer, default=0, comment='独立会话数')
    unique_ips = Column(Integer, default=0, comment='独立IP数')
    
    # 更新时间
    last_calculated = Column(DateTime, default=func.now(), comment='最后计算时间')
    
    # 关联关系
    user = relationship("User", backref="download_summary")
    
    def __init__(self, user_id: int = None):
        self.user_id = user_id
        self.favorite_file_types = {}
    
    def update_from_activity(self, activity: UserDownloadActivity):
        """从活动记录更新汇总统计"""
        self.total_downloads += activity.total_downloads
        self.total_size += activity.total_size
        self.single_downloads += activity.single_downloads
        self.batch_downloads += activity.batch_downloads
        self.folder_downloads += activity.folder_downloads
        self.encrypted_downloads += activity.encrypted_downloads
        self.password_requests += activity.password_requests
        
        # 更新时间统计
        if activity.first_download_time:
            if not self.first_download or activity.first_download_time < self.first_download:
                self.first_download = activity.first_download_time
        
        if activity.last_download_time:
            if not self.last_download or activity.last_download_time > self.last_download:
                self.last_download = activity.last_download_time
        
        # 更新文件类型偏好
        if activity.file_types:
            if not self.favorite_file_types:
                self.favorite_file_types = {}
            for ext, count in activity.file_types.items():
                self.favorite_file_types[ext] = self.favorite_file_types.get(ext, 0) + count
        
        # 更新活跃天数
        self.active_days += 1
        
        # 更新最后计算时间
        self.last_calculated = datetime.now()
    
    def get_download_frequency(self) -> float:
        """获取下载频率（每天平均下载次数）"""
        if self.active_days == 0:
            return 0.0
        return self.total_downloads / self.active_days
    
    def get_encryption_ratio(self) -> float:
        """获取加密下载比例"""
        if self.total_downloads == 0:
            return 0.0
        return self.encrypted_downloads / self.total_downloads
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'total_downloads': self.total_downloads,
            'total_size': self.total_size,
            'total_files': self.total_files,
            'single_downloads': self.single_downloads,
            'batch_downloads': self.batch_downloads,
            'folder_downloads': self.folder_downloads,
            'encrypted_downloads': self.encrypted_downloads,
            'password_requests': self.password_requests,
            'successful_requests': self.successful_requests,
            'first_download': self.first_download.isoformat() if self.first_download else None,
            'last_download': self.last_download.isoformat() if self.last_download else None,
            'most_active_day': self.most_active_day.isoformat() if self.most_active_day else None,
            'most_active_hour': self.most_active_hour,
            'favorite_file_types': self.favorite_file_types,
            'preferred_download_source': self.preferred_download_source,
            'avg_batch_size': self.avg_batch_size,
            'active_days': self.active_days,
            'unique_sessions': self.unique_sessions,
            'unique_ips': self.unique_ips,
            'last_calculated': self.last_calculated.isoformat() if self.last_calculated else None,
            'download_frequency': self.get_download_frequency(),
            'encryption_ratio': self.get_encryption_ratio()
        } 